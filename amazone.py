from rembg import remove
from PIL import Image, ImageFilter, ImageChops
import io

def improve_alpha_edges(alpha_channel, blur_radius=1, threshold=10):
    """
    تحسين حواف قناة ألفا لتنعيمها مع الحفاظ على التفاصيل.
    """
    blurred = alpha_channel.filter(ImageFilter.GaussianBlur(radius=blur_radius))
    mask = blurred.point(lambda p: 255 if p > threshold else 0)
    improved_alpha = ImageChops.multiply(blurred, mask)
    return improved_alpha

def remove_bg_and_add_white_bg(input_path, output_path_jpg):
    # 1. اقرأ الصورة الأصلية
    with open(input_path, 'rb') as f:
        input_bytes = f.read()

    # 2. إزالة الخلفية
    result_bytes = remove(input_bytes)

    # 3. فتح الصورة الناتجة بخلفية شفافة
    img_no_bg = Image.open(io.BytesIO(result_bytes)).convert("RGBA")

    # 4. تحسين قناة الشفافية (ألفا)
    alpha = img_no_bg.split()[3]
    improved_alpha = improve_alpha_edges(alpha, blur_radius=1.5, threshold=15)
    img_no_bg.putalpha(improved_alpha)

    # 5. دمج مع خلفية بيضاء
    white_bg = Image.new("RGBA", img_no_bg.size, (255, 255, 255, 255))
    final_image = Image.alpha_composite(white_bg, img_no_bg).convert("RGB")

    # 6. حفظ الصورة النهائية كـ JPG
    final_image.save(output_path_jpg, format="JPEG", quality=95)
    print(f"✅ تم حفظ الصورة بخلفية بيضاء نقيّة: {output_path_jpg}")

if __name__ == "__main__":
    input_file = "ش7.jpg"                         # استبدل باسم صورتك الأصلية
    output_jpg = "ش7_جاهزة_لأمازون.jpg"          # اسم الصورة النهائية

    remove_bg_and_add_white_bg(input_file, output_jpg)
